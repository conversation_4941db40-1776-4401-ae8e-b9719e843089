<script setup lang="ts">
import { useFileDialog } from '@vueuse/core'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { format as dateFormat } from 'date-fns'

definePageMeta({
  title: 'Dashboard Layout',
  layout: 'dashboard'
})

const { user: currentUser } = useUserSession()

const userSchem = toTypedSchema(
  z.object({
    name: z.string().min(3),
    image: z.string().optional(),
    email: z.string().email('Invalid email'),
  }),
)

const form = useForm({
  validationSchema: userSchem,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    await authClient.updateUser({
      name: values.name
    }, {
      onSuccess: (data) => {
        // 例如显示成功消息
        toast.success('用户信息更新成功')
      },
      onError: (error) => {
        toast.error(error.error.message)
      }
    })
    loading.value = false
  }
  catch (error) {
    console.error(error)
    toast.error(error.data?.message || 'An error occurred while updating the profile.')
  }
  finally {
    loading.value = false
  }
})

const fileUrl = ref(null)
const fileUploading = ref(false)
const { files, open: openFileDialog, onChange: onFileSelected } = useFileDialog({
  accept: 'image/*',
  multiple: false,
})

onFileSelected(async () => {
  const file = files.value[0]
  const formData = new FormData()
  formData.append('image', file)
  try {
    fileUploading.value = true
    const image = await $fetch('/api/images/upload', {
      method: 'POST',
      body: formData,
    }) as { url: string }

    // 使用 better-auth 的 updateUser 方法更新头像
    await authClient.updateUser({
      image: image.url
    }, {
      onSuccess: () => {
        toast.success('Avatar updated successfully')
      },
      onError: (error) => {
        toast.error(error.error.message || 'Failed to update avatar')
      }
    })
  }
  catch (error) {
    toast.error('Failed to upload image')
  }
  finally {
    fileUploading.value = false
  }
})

// 使用 better-auth 的内置账户管理功能
const { data: oauthAccounts, refresh: refreshOauthAccount } = await useLazyAsyncData('oauth-accounts', async () => {
  const accounts = await authClient.listAccounts()
  return accounts.data || []
})

const unlinking = ref<string | null>(null)
const getProviderIcon = (provider: string) => {
  const icons: Record<string, string> = {
    google: 'custom:ri-google-fill',
    github: 'ri:github-fill',
    twitter: 'ri:twitter-x-fill',
  }
  return icons[provider] || 'i-ph-link'
}

const unlinkOauthAccount = async (account: any) => {
  try {
    unlinking.value = account.id

    // 安全检查：确保用户不会失去所有登录方式
    const accountCount = oauthAccounts.value?.length || 0

    // 检查是否只有一个账户（即将要删除的这个）
    if (accountCount <= 1) {
      toast.error('Cannot unlink your only sign-in method. Please add another sign-in method or set a password first.')
      return
    }

    const { error } = await authClient.unlinkAccount({
      providerId: account.provider,
      accountId: account.accountId
    })

    if (error) {
      throw new Error(error.message || 'Failed to unlink account')
    }

    toast.success('Account unlinked successfully')
    await refreshOauthAccount()
  }
  catch (error) {
    console.error(error)
    toast.error(error.message || 'Failed to unlink account')
  }
  finally {
    unlinking.value = null
  }
}
</script>

<template>
  <section
    id="profile"
    class="mx-auto w-full max-w-full px-6 md:max-w-5xl"
  >
    <div class="flex justify-between py-8">
      <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
        Settings
      </h1>
    </div>

    <DashboardTabNavs />

    <div
      class="flex flex-col gap-6"
      style="opacity: 1; transform: none;"
    >
      <section class="rounded-lg border">
        <form
          id="userForm"
          @submit="onSubmit"
        >
          <div class="border-b px-6 py-4">
            <h2 class="font-bold">
              Profile
            </h2>
          </div>
          <div class="mb-2 flex max-w-md flex-col gap-4 p-4">
            <div class="flex items-center gap-4">
              <Avatar class="rounded-lg">
                <AvatarImage
                  :src="currentUser?.image || ''"
                  :alt="currentUser?.name || ''"
                />
                <AvatarFallback class="rounded-lg">
                  {{ avatarName(currentUser?.name || '') }}
                </AvatarFallback>
              </Avatar>
              <Button
                variant="secondary"
                @click="openFileDialog"
              >
                <Icon
                  v-if="fileUploading"
                  name="svg-spinners:3-dots-fade"
                  class="mr-2"
                /> Change
              </Button>
            </div>
            <FormField
              v-slot="{ componentField }"
              name="email"
              :value="currentUser?.email || ''"
            >
              <FormItem>
                <FormLabel
                  class="text-muted-foreground"
                  for="email"
                >
                  Email
                </FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    disabled
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
            <FormField
              v-slot="{ componentField }"
              name="name"
              :value="currentUser?.name || ''"
            >
              <FormItem>
                <FormLabel
                  class="text-muted-foreground"
                  for="name"
                >
                  name
                </FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    v-bind="componentField"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>
          <div class="flex justify-between border-t py-3 pl-5 pr-3">
            <Button>Update</Button>
          </div>
        </form>
      </section>

      <section class="rounded-lg border">
        <div class="border-b px-6 py-4">
          <h2 class="font-bold">
            Oauth Account
          </h2>
        </div>
        <div class="mb-2 flex flex-col gap-4 p-4">
          <p
            v-if="oauthAccounts && oauthAccounts.length === 0"
            class="text-center text-muted-foreground"
          >
            You don't have any linked accounts.
          </p>
          <ul
            v-else
            role="list"
            class="divide-y"
          >
            <li
              v-for="oauthAccount in oauthAccounts"
              :key="oauthAccount.id"
              class="flex items-center justify-between gap-6 py-4"
            >
              <div class="flex gap-x-4">
                <Icon
                  :name="getProviderIcon(oauthAccount.provider)"
                  class="size-6 flex-none rounded-full"
                />
                <div class="flex min-w-0 flex-auto items-center gap-x-2">
                  <p class="text-sm font-semibold capitalize leading-6">
                    {{ oauthAccount.provider }}
                  </p>
                  <p class="mt-px truncate text-xs leading-5 text-muted-foreground">
                    Connected on {{ dateFormat(oauthAccount.createdAt, "dd MMMM yyyy") }}
                  </p>
                </div>
              </div>
              <Button
                variant="destructive"
                size="sm"
                :disabled="unlinking === oauthAccount.id"
                @click="unlinkOauthAccount(oauthAccount)"
              >
                <Icon
                  v-if="unlinking === oauthAccount.id"
                  name="svg-spinners:3-dots-fade"
                  class="mr-2"
                /> Unlink
              </Button>
            </li>
          </ul>
        </div>
      </section>
    </div>
  </section>
</template>
